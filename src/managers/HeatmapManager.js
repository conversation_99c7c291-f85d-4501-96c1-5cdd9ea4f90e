/**
 * 热力图管理器
 * 负责管理高德地图热力图图层
 */

import { HEATMAP_CONFIG } from '../data/weatherConfig.js'

export class HeatmapManager {
  constructor(map, AMap) {
    this.map = map
    this.AMap = AMap
    this.heatmapLayer = null
    this.isVisible = false
    this.heatmapData = []
    this.isInitialized = false
  }

  /**
   * 初始化热力图图层
   * @returns {Promise<boolean>} 是否初始化成功
   */
  async initHeatmap() {
    try {
      // 检查热力图插件是否可用
      if (!this.AMap.HeatMap) {
        console.log('正在加载热力图插件...')
        await this.loadHeatmapPlugin()
      }

      // 创建热力图图层
      this.heatmapLayer = new this.AMap.HeatMap(this.map, {
        radius: HEATMAP_CONFIG.style.radius,
        opacity: HEATMAP_CONFIG.style.opacity,
        gradient: HEATMAP_CONFIG.style.gradient,
        zooms: [3, 18],
        zIndex: 1, // 设置最低的层级，确保在所有电力线路和设施图标下方
        visible: false // 默认隐藏
      })

      this.isInitialized = true
      console.log('热力图图层初始化成功')
      return true
    } catch (error) {
      console.error('热力图初始化失败:', error)
      return false
    }
  }

  /**
   * 加载热力图插件
   * @returns {Promise<void>}
   */
  async loadHeatmapPlugin() {
    return new Promise((resolve, reject) => {
      // 动态加载热力图插件
      this.AMap.plugin(['AMap.HeatMap'], () => {
        if (this.AMap.HeatMap) {
          console.log('热力图插件加载成功')
          resolve()
        } else {
          reject(new Error('热力图插件加载失败'))
        }
      })
    })
  }

  /**
   * 设置热力图数据
   * @param {Array} data - 热力图数据点数组
   */
  setData(data) {
    if (!this.isInitialized) {
      console.warn('热力图未初始化，无法设置数据')
      return
    }

    this.heatmapData = data
    
    // 转换数据格式为高德地图热力图所需格式
    const heatmapPoints = data.map(point => ({
      lng: point.lng,
      lat: point.lat,
      count: point.count || HEATMAP_CONFIG.dataPoint.defaultIntensity
    }))

    // 设置热力图数据
    this.heatmapLayer.setDataSet({
      data: heatmapPoints,
      max: HEATMAP_CONFIG.dataPoint.maxIntensity
    })

    console.log(`热力图数据已更新，共${heatmapPoints.length}个数据点`)
  }

  /**
   * 显示热力图
   */
  show() {
    if (!this.isInitialized) {
      console.warn('热力图未初始化，无法显示')
      return
    }

    this.heatmapLayer.show()
    this.isVisible = true
    console.log('热力图已显示')
  }

  /**
   * 隐藏热力图
   */
  hide() {
    if (!this.isInitialized) {
      return
    }

    this.heatmapLayer.hide()
    this.isVisible = false
    console.log('热力图已隐藏')
  }

  /**
   * 切换热力图显示状态
   * @returns {boolean} 当前显示状态
   */
  toggle() {
    if (this.isVisible) {
      this.hide()
    } else {
      this.show()
    }
    return this.isVisible
  }

  /**
   * 更新热力图样式
   * @param {Object} styleConfig - 样式配置
   */
  updateStyle(styleConfig) {
    if (!this.isInitialized) {
      console.warn('热力图未初始化，无法更新样式')
      return
    }

    const config = { ...HEATMAP_CONFIG.style, ...styleConfig }
    
    // 重新创建热力图图层以应用新样式
    const wasVisible = this.isVisible
    
    // 移除旧图层
    if (this.heatmapLayer) {
      this.map.remove(this.heatmapLayer)
    }

    // 创建新图层
    this.heatmapLayer = new this.AMap.HeatMap(this.map, {
      radius: config.radius,
      opacity: config.opacity,
      gradient: config.gradient,
      zooms: [3, 18],
      visible: wasVisible
    })

    // 重新设置数据
    if (this.heatmapData.length > 0) {
      this.setData(this.heatmapData)
    }

    this.isVisible = wasVisible
    console.log('热力图样式已更新')
  }

  /**
   * 获取热力图可见性状态
   * @returns {boolean} 是否可见
   */
  getVisibility() {
    return this.isVisible
  }

  /**
   * 获取热力图数据点数量
   * @returns {number} 数据点数量
   */
  getDataCount() {
    return this.heatmapData.length
  }

  /**
   * 清空热力图数据
   */
  clearData() {
    if (!this.isInitialized) {
      return
    }

    this.heatmapData = []
    this.heatmapLayer.setDataSet({
      data: [],
      max: HEATMAP_CONFIG.dataPoint.maxIntensity
    })

    console.log('热力图数据已清空')
  }

  /**
   * 销毁热力图图层
   */
  destroy() {
    if (this.heatmapLayer) {
      this.map.remove(this.heatmapLayer)
      this.heatmapLayer = null
    }
    
    this.isInitialized = false
    this.isVisible = false
    this.heatmapData = []
    
    console.log('热力图图层已销毁')
  }

  /**
   * 获取指定位置的温度信息
   * @param {Array} lngLat - 经纬度坐标 [lng, lat]
   * @param {number} radius - 搜索半径（米）
   * @returns {Object|null} 温度信息
   */
  getTemperatureAt(lngLat, radius = 10000) {
    if (this.heatmapData.length === 0) {
      return null
    }

    // 计算距离并找到最近的数据点
    let nearestPoint = null
    let minDistance = Infinity

    this.heatmapData.forEach(point => {
      const distance = this.calculateDistance(lngLat, [point.lng, point.lat])
      if (distance < radius && distance < minDistance) {
        minDistance = distance
        nearestPoint = point
      }
    })

    return nearestPoint ? {
      temperature: nearestPoint.temperature,
      place: nearestPoint.place,
      weather: nearestPoint.weather,
      distance: Math.round(minDistance)
    } : null
  }

  /**
   * 计算两点间距离（米）
   * @param {Array} point1 - 点1 [lng, lat]
   * @param {Array} point2 - 点2 [lng, lat]
   * @returns {number} 距离（米）
   */
  calculateDistance(point1, point2) {
    const [lng1, lat1] = point1
    const [lng2, lat2] = point2
    
    const R = 6371000 // 地球半径（米）
    const dLat = (lat2 - lat1) * Math.PI / 180
    const dLng = (lng2 - lng1) * Math.PI / 180
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
              Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
              Math.sin(dLng / 2) * Math.sin(dLng / 2)
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
    
    return R * c
  }

  /**
   * 获取热力图统计信息
   * @returns {Object} 统计信息
   */
  getStatistics() {
    if (this.heatmapData.length === 0) {
      return {
        dataCount: 0,
        isVisible: this.isVisible,
        isInitialized: this.isInitialized
      }
    }

    const temperatures = this.heatmapData
      .map(point => point.temperature)
      .filter(temp => temp !== undefined)

    return {
      dataCount: this.heatmapData.length,
      temperatureCount: temperatures.length,
      minTemperature: Math.min(...temperatures),
      maxTemperature: Math.max(...temperatures),
      averageTemperature: temperatures.reduce((sum, temp) => sum + temp, 0) / temperatures.length,
      isVisible: this.isVisible,
      isInitialized: this.isInitialized
    }
  }
}
