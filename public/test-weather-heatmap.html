<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>天气热力图测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Microsoft YaHei', sans-serif;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 24px;
        }
        
        .content {
            padding: 20px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background: #fafafa;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        .test-button {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(116, 185, 255, 0.4);
        }
        
        .test-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .result {
            margin-top: 15px;
            padding: 15px;
            background: white;
            border-radius: 4px;
            border-left: 4px solid #667eea;
            font-family: 'Consolas', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .success {
            border-left-color: #00b894;
            background: #f8fff8;
        }
        
        .error {
            border-left-color: #e17055;
            background: #fff8f8;
        }
        
        .loading {
            color: #667eea;
            font-style: italic;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .stat-item {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #e0e0e0;
            text-align: center;
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌡️ 天气热力图功能测试</h1>
            <p>测试云南省天气API和热力图功能</p>
        </div>
        
        <div class="content">
            <!-- API测试 -->
            <div class="test-section">
                <h3>🔌 天气API测试</h3>
                <button class="test-button" onclick="testSingleWeatherAPI()">测试单个地区API</button>
                <button class="test-button" onclick="testBatchWeatherAPI()">测试批量API</button>
                <button class="test-button" onclick="testWeatherManager()">测试天气管理器</button>
                <div id="api-result" class="result" style="display: none;"></div>
            </div>
            
            <!-- 数据处理测试 -->
            <div class="test-section">
                <h3>📊 数据处理测试</h3>
                <button class="test-button" onclick="testDataProcessing()">测试数据处理</button>
                <button class="test-button" onclick="testHeatmapData()">测试热力图数据</button>
                <div id="data-result" class="result" style="display: none;"></div>
                <div id="stats" class="stats" style="display: none;"></div>
            </div>
            
            <!-- 配置测试 -->
            <div class="test-section">
                <h3>⚙️ 配置测试</h3>
                <button class="test-button" onclick="testConfig()">测试配置文件</button>
                <button class="test-button" onclick="testPlaceMapping()">测试地区映射</button>
                <div id="config-result" class="result" style="display: none;"></div>
            </div>
        </div>
    </div>

    <script type="module">
        // 测试用的天气API配置
        const WEATHER_API_CONFIG = {
            baseUrl: 'http://***********/api/tianqi/tqyb.php',
            id: '10006691',
            key: '1c6a6f2d1679b548a63fee60af92c5ad',
            sheng: '云南'
        }

        // 测试地区列表
        const TEST_PLACES = ['昆明', '大理', '丽江', '香格里拉', '西双版纳']

        // 显示结果
        function showResult(elementId, content, type = 'info') {
            const element = document.getElementById(elementId)
            element.style.display = 'block'
            element.className = `result ${type}`
            element.innerHTML = content
        }

        // 显示统计信息
        function showStats(data) {
            const statsElement = document.getElementById('stats')
            if (!data) {
                statsElement.style.display = 'none'
                return
            }

            statsElement.style.display = 'grid'
            statsElement.innerHTML = `
                <div class="stat-item">
                    <div class="stat-value">${data.totalPlaces || 0}</div>
                    <div class="stat-label">数据点</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">${data.minTemperature || 0}°C</div>
                    <div class="stat-label">最低温度</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">${data.maxTemperature || 0}°C</div>
                    <div class="stat-label">最高温度</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">${data.averageTemperature || 0}°C</div>
                    <div class="stat-label">平均温度</div>
                </div>
            `
        }

        // 测试单个天气API
        window.testSingleWeatherAPI = async function() {
            showResult('api-result', '<div class="loading">正在测试单个地区API...</div>')
            
            try {
                const place = '香格里拉'
                const url = new URL(WEATHER_API_CONFIG.baseUrl)
                url.searchParams.set('id', WEATHER_API_CONFIG.id)
                url.searchParams.set('key', WEATHER_API_CONFIG.key)
                url.searchParams.set('sheng', WEATHER_API_CONFIG.sheng)
                url.searchParams.set('place', place)

                const response = await fetch(url.toString())
                const data = await response.json()

                if (data.code === 200) {
                    showResult('api-result', `
                        <strong>✅ API测试成功</strong><br>
                        地区: ${data.place}<br>
                        温度: ${data.temperature}°C<br>
                        天气: ${data.weather1}<br>
                        湿度: ${data.humidity}%<br>
                        风向: ${data.windDirection}<br>
                        更新时间: ${data.uptime}
                    `, 'success')
                } else {
                    showResult('api-result', `❌ API返回错误: ${data.code}`, 'error')
                }
            } catch (error) {
                showResult('api-result', `❌ API请求失败: ${error.message}`, 'error')
            }
        }

        // 测试批量天气API
        window.testBatchWeatherAPI = async function() {
            showResult('api-result', '<div class="loading">正在测试批量API...</div>')
            
            try {
                const results = []
                for (const place of TEST_PLACES) {
                    const url = new URL(WEATHER_API_CONFIG.baseUrl)
                    url.searchParams.set('id', WEATHER_API_CONFIG.id)
                    url.searchParams.set('key', WEATHER_API_CONFIG.key)
                    url.searchParams.set('sheng', WEATHER_API_CONFIG.sheng)
                    url.searchParams.set('place', place)

                    try {
                        const response = await fetch(url.toString())
                        const data = await response.json()
                        results.push({ place, data, success: data.code === 200 })
                    } catch (error) {
                        results.push({ place, error: error.message, success: false })
                    }

                    // 添加延迟避免请求过快
                    await new Promise(resolve => setTimeout(resolve, 200))
                }

                const successCount = results.filter(r => r.success).length
                const resultHtml = `
                    <strong>📊 批量API测试完成</strong><br>
                    成功: ${successCount}/${results.length}<br><br>
                    ${results.map(r => 
                        r.success 
                            ? `✅ ${r.place}: ${r.data.temperature}°C, ${r.data.weather1}`
                            : `❌ ${r.place}: ${r.error || '请求失败'}`
                    ).join('<br>')}
                `

                showResult('api-result', resultHtml, successCount > 0 ? 'success' : 'error')
            } catch (error) {
                showResult('api-result', `❌ 批量测试失败: ${error.message}`, 'error')
            }
        }

        // 测试配置文件
        window.testConfig = function() {
            showResult('config-result', '<div class="loading">正在测试配置...</div>')
            
            try {
                const configTest = {
                    apiConfig: WEATHER_API_CONFIG,
                    testPlaces: TEST_PLACES,
                    heatmapConfig: {
                        temperatureRange: { min: -10, max: 40 },
                        style: {
                            radius: 50,
                            opacity: [0, 0.8],
                            gradient: {
                                0.0: '#0000FF',
                                0.2: '#00FFFF',
                                0.4: '#00FF00',
                                0.6: '#FFFF00',
                                0.8: '#FF8000',
                                1.0: '#FF0000'
                            }
                        }
                    }
                }

                showResult('config-result', `
                    <strong>✅ 配置测试通过</strong><br>
                    API配置: ✓<br>
                    测试地区: ${TEST_PLACES.length}个<br>
                    热力图配置: ✓<br>
                    温度范围: ${configTest.heatmapConfig.temperatureRange.min}°C ~ ${configTest.heatmapConfig.temperatureRange.max}°C
                `, 'success')
            } catch (error) {
                showResult('config-result', `❌ 配置测试失败: ${error.message}`, 'error')
            }
        }

        // 其他测试函数的占位符
        window.testWeatherManager = function() {
            showResult('api-result', '⚠️ 需要在Vue应用中测试天气管理器', 'error')
        }

        window.testDataProcessing = function() {
            showResult('data-result', '⚠️ 需要在Vue应用中测试数据处理', 'error')
        }

        window.testHeatmapData = function() {
            showResult('data-result', '⚠️ 需要在Vue应用中测试热力图数据', 'error')
        }

        window.testPlaceMapping = function() {
            showResult('config-result', '⚠️ 需要在Vue应用中测试地区映射', 'error')
        }

        // 页面加载完成后自动测试配置
        document.addEventListener('DOMContentLoaded', function() {
            console.log('天气热力图测试页面加载完成')
            testConfig()
        })
    </script>
</body>
</html>
